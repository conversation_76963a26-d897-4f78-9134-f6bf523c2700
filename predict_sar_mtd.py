# predict_sar_mtd.py
"""
SAR图像动目标检测预测脚本
使用训练好的YOLOv8模型进行双流SAR图像动目标检测
"""

import torch
import cv2
import numpy as np
import os
import logging
import argparse
import yaml
from pathlib import Path
from typing import List, Tuple, Dict, Optional
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from ultralytics import YOLO

# 导入数据处理函数
from dataset_loader import multi_frame_spatial_fusion, robust_motion_detection, enhance_contrast_stretch

# 导入卡尔曼滤波相关模块
from target_tracker import MultiTargetTracker

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SARMTDPredictor:
    """SAR图像动目标检测预测器"""
    
    def __init__(self, model_path: str, sequence_length: int = 4,
                 motion_method: str = 'temporal_std',
                 spatial_method: str = 'weighted_average',
                 conf_threshold: float = 0.25,
                 iou_threshold: float = 0.45,
                 use_sliding_window: bool = True,
                 sliding_step: int = 1,
                 keep_only_best: bool = False,
                 use_kalman_filter: bool = True,
                 max_disappeared: int = 30,
                 max_distance: float = 50.0):
        """
        初始化预测器

        Args:
            model_path: 训练好的模型权重路径
            sequence_length: 输入序列长度
            motion_method: 运动检测方法
            spatial_method: 空间融合方法
            conf_threshold: 置信度阈值
            iou_threshold: IoU阈值
            use_sliding_window: 是否使用滑窗检测
            sliding_step: 滑窗步长
            keep_only_best: 是否只保留置信度最高的目标
            use_kalman_filter: 是否使用卡尔曼滤波跟踪
            max_disappeared: 目标消失的最大帧数
            max_distance: 数据关联的最大距离
        """
        self.model_path = model_path
        self.sequence_length = sequence_length
        self.motion_method = motion_method
        self.spatial_method = spatial_method
        self.conf_threshold = conf_threshold
        self.iou_threshold = iou_threshold
        self.use_sliding_window = use_sliding_window
        self.sliding_step = sliding_step
        self.keep_only_best = keep_only_best
        self.use_kalman_filter = use_kalman_filter
        self.max_disappeared = max_disappeared
        self.max_distance = max_distance

        # 加载模型
        self.model = self._load_model()

        # 初始化多目标跟踪器
        if self.use_kalman_filter:
            self.tracker = MultiTargetTracker(
                max_disappeared=max_disappeared,
                max_distance=max_distance,
                use_adaptive=True,
                iou_threshold=0.3
            )
        else:
            self.tracker = None
        
        logger.info(f"SAR MTD预测器初始化完成")
        logger.info(f"模型路径: {model_path}")
        logger.info(f"序列长度: {sequence_length}")
        logger.info(f"置信度阈值: {conf_threshold}")
        logger.info(f"滑窗检测: {'启用' if use_sliding_window else '禁用'}")
        if use_sliding_window:
            logger.info(f"滑窗步长: {sliding_step}")
        logger.info(f"只保留最佳目标: {'是' if keep_only_best else '否'}")
        logger.info(f"卡尔曼滤波跟踪: {'启用' if use_kalman_filter else '禁用'}")
        if use_kalman_filter:
            logger.info(f"最大消失帧数: {max_disappeared}")
            logger.info(f"最大关联距离: {max_distance}")
    
    def _load_model(self) -> YOLO:
        """加载训练好的YOLO模型"""
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"模型文件不存在: {self.model_path}")
        
        try:
            model = YOLO(self.model_path)
            logger.info(f" 模型加载成功: {self.model_path}")
            return model
        except Exception as e:
            logger.error(f" 模型加载失败: {e}")
            raise
    
    def _load_image_sequence(self, image_paths: List[str]) -> np.ndarray:
        """
        加载图像序列
        
        Args:
            image_paths: 图像文件路径列表
            
        Returns:
            形状为 (T, H, W) 的图像序列
        """
        image_sequence = []
        
        for path in image_paths:
            if not os.path.exists(path):
                raise FileNotFoundError(f"图像文件不存在: {path}")
            
            # 读取灰度图像
            img = cv2.imread(path, cv2.IMREAD_GRAYSCALE)
            if img is None:
                raise IOError(f"无法读取图像: {path}")
            
            # 转换为float32并归一化到[0,1]
            img = img.astype(np.float32) / 255.0
            image_sequence.append(img)
        
        return np.array(image_sequence)
    
    def _generate_two_stream_input(self, image_sequence: np.ndarray) -> np.ndarray:
        """
        生成双流输入
        
        Args:
            image_sequence: 形状为 (T, H, W) 的图像序列
            
        Returns:
            形状为 (2, H, W) 的双流输入 [spatial_channel, motion_channel]
        """
        # 生成空间流（静态背景信息）
        spatial_frame = multi_frame_spatial_fusion(image_sequence, self.spatial_method)
        
        # 生成运动流（运动目标信息）
        motion_map = robust_motion_detection(image_sequence, self.motion_method)
        
        # 对比度增强
        spatial_frame = enhance_contrast_stretch(spatial_frame)
        motion_map = enhance_contrast_stretch(motion_map)
        
        # 确保是2D数组
        spatial_frame = np.squeeze(spatial_frame)
        motion_map = np.squeeze(motion_map)
        
        # 堆叠为2通道图像 (2, H, W)
        two_stream_input = np.stack([spatial_frame, motion_map], axis=0)
        
        return two_stream_input.astype(np.float32)

    def _filter_best_detection(self, detections: List[Dict]) -> List[Dict]:
        """
        只保留置信度最高的检测目标

        Args:
            detections: 检测结果列表

        Returns:
            过滤后的检测结果列表
        """
        if not detections:
            return detections

        if self.keep_only_best:
            # 找到置信度最高的目标
            best_detection = max(detections, key=lambda x: x['confidence'])
            logger.info(f" 过滤检测结果: {len(detections)} -> 1 (保留最佳目标，置信度: {best_detection['confidence']:.3f})")
            return [best_detection]
        else:
            return detections

    def predict_sequence(self, image_paths: List[str],
                        save_path: Optional[str] = None,
                        visualize: bool = True,
                        use_sliding_window: Optional[bool] = None) -> Dict:
        """
        对图像序列进行预测

        Args:
            image_paths: 图像文件路径列表
            save_path: 结果保存路径
            visualize: 是否可视化结果
            use_sliding_window: 是否使用滑窗检测，None时使用初始化时的设置

        Returns:
            预测结果字典
        """
        if len(image_paths) < self.sequence_length:
            raise ValueError(f"图像序列长度不足: {len(image_paths)} < {self.sequence_length}")

        # 确定是否使用滑窗检测
        use_sliding = use_sliding_window if use_sliding_window is not None else self.use_sliding_window

        if use_sliding and len(image_paths) > self.sequence_length:
            logger.info(" 使用滑窗检测模式")
            return self.predict_with_sliding_window(image_paths, save_path, visualize)
        else:
            if not use_sliding:
                logger.info(" 使用单序列检测模式")
            else:
                logger.info(" 图像数量等于序列长度，使用单序列检测模式")

            # 取前sequence_length帧
            selected_paths = image_paths[:self.sequence_length]
        
        # 加载图像序列
        logger.info(f"加载图像序列: {len(selected_paths)} 帧")
        image_sequence = self._load_image_sequence(selected_paths)
        
        # 生成双流输入
        logger.info("生成双流特征...")
        two_stream_input = self._generate_two_stream_input(image_sequence)
        
        # 转换为RGB格式用于YOLO预测（复制第一个通道作为第三个通道）
        # YOLO期望3通道输入，我们将2通道扩展为3通道
        rgb_input = np.zeros((3, two_stream_input.shape[1], two_stream_input.shape[2]), dtype=np.float32)
        rgb_input[0] = two_stream_input[0]  # 空间流 -> R通道
        rgb_input[1] = two_stream_input[0]  # 空间流 -> G通道
        rgb_input[2] = two_stream_input[1]  # 运动流 -> B通道
        
        # 转换为HWC格式并转换为uint8
        rgb_input = np.transpose(rgb_input, (1, 2, 0))
        rgb_input = (rgb_input * 255).astype(np.uint8)
        
        # 执行预测
        logger.info("执行目标检测...")
        results = self.model.predict(
            rgb_input,
            conf=self.conf_threshold,
            iou=self.iou_threshold,
            verbose=False
        )
        
        # 解析预测结果
        result = results[0]
        boxes = result.boxes
        
        predictions = {
            'image_paths': selected_paths,
            'detections': [],
            'spatial_frame': two_stream_input[0],
            'motion_frame': two_stream_input[1],
            'input_shape': rgb_input.shape
        }
        
        if boxes is not None and len(boxes) > 0:
            for box in boxes:
                detection = {
                    'bbox': box.xyxy[0].cpu().numpy().tolist(),  # [x1, y1, x2, y2] 转换为Python列表
                    'confidence': float(box.conf[0].cpu().numpy()),
                    'class': int(box.cls[0].cpu().numpy())
                }
                predictions['detections'].append(detection)

        # 应用最佳目标过滤
        predictions['detections'] = self._filter_best_detection(predictions['detections'])

        logger.info(f"检测到 {len(predictions['detections'])} 个目标")
        
        # 可视化和保存结果
        if visualize or save_path:
            self._visualize_results(predictions, save_path)
        
        return predictions

    def _predict_single_window(self, window_paths: List[str], window_id: int) -> Dict:
        """
        对单个滑窗进行预测

        Args:
            window_paths: 当前窗口的图像路径列表
            window_id: 窗口ID

        Returns:
            当前窗口的预测结果
        """
        # 加载图像序列
        image_sequence = self._load_image_sequence(window_paths)

        # 生成双流输入
        two_stream_input = self._generate_two_stream_input(image_sequence)

        # 转换为RGB格式用于YOLO预测
        rgb_input = np.zeros((3, two_stream_input.shape[1], two_stream_input.shape[2]), dtype=np.float32)
        rgb_input[0] = two_stream_input[0]  # 空间流 -> R通道
        rgb_input[1] = two_stream_input[0]  # 空间流 -> G通道
        rgb_input[2] = two_stream_input[1]  # 运动流 -> B通道

        # 转换为HWC格式并转换为uint8
        rgb_input = np.transpose(rgb_input, (1, 2, 0))
        rgb_input = (rgb_input * 255).astype(np.uint8)

        # 执行预测
        results = self.model.predict(
            rgb_input,
            conf=self.conf_threshold,
            iou=self.iou_threshold,
            verbose=False
        )

        # 解析预测结果
        result = results[0]
        boxes = result.boxes

        predictions = {
            'window_paths': window_paths,
            'detections': [],
            'spatial_frame': two_stream_input[0],
            'motion_frame': two_stream_input[1],
            'input_shape': rgb_input.shape
        }

        if boxes is not None and len(boxes) > 0:
            for box in boxes:
                detection = {
                    'bbox': box.xyxy[0].cpu().numpy().tolist(),  # [x1, y1, x2, y2] 转换为Python列表
                    'confidence': float(box.conf[0].cpu().numpy()),
                    'class': int(box.cls[0].cpu().numpy())
                }
                predictions['detections'].append(detection)

        # 应用最佳目标过滤
        predictions['detections'] = self._filter_best_detection(predictions['detections'])

        return predictions

    def predict_with_sliding_window(self, image_paths: List[str],
                                   save_path: Optional[str] = None,
                                   visualize: bool = True,
                                   use_kalman_filter: Optional[bool] = None) -> Dict:
        """
        使用滑窗方法对图像序列进行预测

        Args:
            image_paths: 图像文件路径列表
            save_path: 结果保存路径
            visualize: 是否可视化结果
            use_kalman_filter: 是否使用卡尔曼滤波，None时使用初始化设置

        Returns:
            预测结果字典，包含所有滑窗的检测结果和跟踪轨迹
        """
        if len(image_paths) < self.sequence_length:
            raise ValueError(f"图像序列长度不足: {len(image_paths)} < {self.sequence_length}")

        # 确定是否使用卡尔曼滤波
        use_kalman = use_kalman_filter if use_kalman_filter is not None else self.use_kalman_filter

        logger.info(f"   开始滑窗检测: {len(image_paths)} 张图像")
        logger.info(f"   序列长度: {self.sequence_length}, 步长: {self.sliding_step}")
        logger.info(f"   卡尔曼滤波: {'启用' if use_kalman else '禁用'}")

        # 计算滑窗数量
        num_windows = (len(image_paths) - self.sequence_length) // self.sliding_step + 1
        logger.info(f"   将生成 {num_windows} 个滑窗序列")

        all_detections = []
        all_windows_info = []
        all_tracks = []  # 存储跟踪轨迹

        # 重置跟踪器
        if use_kalman and self.tracker:
            self.tracker.reset()

        # 滑窗遍历
        for i in range(0, len(image_paths) - self.sequence_length + 1, self.sliding_step):
            window_paths = image_paths[i:i + self.sequence_length]
            window_id = i // self.sliding_step + 1

            logger.info(f"   处理滑窗 {window_id}/{num_windows}: 帧 {i+1}-{i+self.sequence_length}")

            # 对当前窗口进行预测
            window_predictions = self._predict_single_window(window_paths, window_id)

            # 使用卡尔曼滤波进行跟踪
            current_tracks = []
            if use_kalman and self.tracker:
                # 更新跟踪器
                current_tracks = self.tracker.update(window_predictions['detections'])
                logger.debug(f"   滑窗 {window_id}: {len(window_predictions['detections'])} 个检测, {len(current_tracks)} 个轨迹")

            # 记录窗口信息
            window_info = {
                'window_id': window_id,
                'start_frame': i + 1,
                'end_frame': i + self.sequence_length,
                'image_paths': window_paths,
                'detections': window_predictions['detections'],
                'tracks': current_tracks,  # 添加跟踪信息
                'spatial_frame': window_predictions['spatial_frame'],
                'motion_frame': window_predictions['motion_frame']
            }
            all_windows_info.append(window_info)

            # 收集所有检测结果
            for det in window_predictions['detections']:
                det['window_id'] = window_id
                det['start_frame'] = i + 1
                all_detections.append(det)

            # 收集跟踪轨迹
            for track in current_tracks:
                track_copy = track.copy()
                track_copy['window_id'] = window_id
                track_copy['start_frame'] = i + 1
                all_tracks.append(track_copy)

        # 合并结果
        merged_predictions = {
            'image_paths': image_paths,
            'total_windows': num_windows,
            'all_detections': all_detections,
            'all_tracks': all_tracks,  # 添加跟踪轨迹
            'windows_info': all_windows_info,
            'sequence_length': self.sequence_length,
            'sliding_step': self.sliding_step,
            'use_kalman_filter': use_kalman
        }

        logger.info(f" 滑窗检测完成: 总共检测到 {len(all_detections)} 个目标")
        if use_kalman:
            logger.info(f"   卡尔曼滤波跟踪: 总共 {len(all_tracks)} 个轨迹点")

        # 可视化和保存结果
        if visualize or save_path:
            if use_kalman:
                self._visualize_sliding_window_with_tracking(merged_predictions, save_path)
            else:
                self._visualize_sliding_window_results(merged_predictions, save_path)

        return merged_predictions

    def _visualize_results(self, predictions: Dict, save_path: Optional[str] = None):
        """可视化预测结果"""
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'Microsoft YaHei']
        plt.rcParams['axes.unicode_minus'] = False

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # 显示空间流
        axes[0, 0].imshow(predictions['spatial_frame'], cmap='gray')
        axes[0, 0].set_title('Spatial Stream', fontsize=14, fontweight='bold')
        axes[0, 0].axis('off')

        # 显示运动流
        axes[0, 1].imshow(predictions['motion_frame'], cmap='hot')
        axes[0, 1].set_title('Motion Stream', fontsize=14, fontweight='bold')
        axes[0, 1].axis('off')

        # 在空间流上显示检测结果
        axes[1, 0].imshow(predictions['spatial_frame'], cmap='gray')
        for det in predictions['detections']:
            bbox = det['bbox']
            conf = det['confidence']
            rect = patches.Rectangle(
                (bbox[0], bbox[1]), bbox[2]-bbox[0], bbox[3]-bbox[1],
                linewidth=2, edgecolor='red', facecolor='none'
            )
            axes[1, 0].add_patch(rect)
            axes[1, 0].text(bbox[0], bbox[1]-5, f'MTD: {conf:.2f}',
                           color='red', fontsize=10, weight='bold')
        axes[1, 0].set_title(f'Detection Results ({len(predictions["detections"])} targets)',
                            fontsize=14, fontweight='bold')
        axes[1, 0].axis('off')

        # 在运动流上显示检测结果
        axes[1, 1].imshow(predictions['motion_frame'], cmap='hot')
        for det in predictions['detections']:
            bbox = det['bbox']
            conf = det['confidence']
            rect = patches.Rectangle(
                (bbox[0], bbox[1]), bbox[2]-bbox[0], bbox[3]-bbox[1],
                linewidth=2, edgecolor='cyan', facecolor='none'
            )
            axes[1, 1].add_patch(rect)
            axes[1, 1].text(bbox[0], bbox[1]-5, f'MTD: {conf:.2f}',
                           color='cyan', fontsize=10, weight='bold')
        axes[1, 1].set_title('Motion Stream Detection', fontsize=14, fontweight='bold')
        axes[1, 1].axis('off')

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"结果已保存到: {save_path}")

        plt.show()

    def _visualize_sliding_window_results(self, merged_predictions: Dict, save_path: Optional[str] = None):
        """可视化滑窗检测结果"""
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'Microsoft YaHei']
        plt.rcParams['axes.unicode_minus'] = False

        windows_info = merged_predictions['windows_info']
        total_windows = merged_predictions['total_windows']

        # 创建子图布局
        if total_windows <= 4:
            rows, cols = 2, 2
        elif total_windows <= 6:
            rows, cols = 2, 3
        elif total_windows <= 9:
            rows, cols = 3, 3
        else:
            rows, cols = 4, 4

        fig, axes = plt.subplots(rows, cols, figsize=(5*cols, 4*rows))
        if total_windows == 1:
            axes = [axes]
        else:
            axes = axes.flatten()

        # 显示每个滑窗的结果
        for i, window_info in enumerate(windows_info):
            if i >= len(axes):
                break

            ax = axes[i]

            # 显示空间流图像
            ax.imshow(window_info['spatial_frame'], cmap='gray')

            # 绘制检测框
            for det in window_info['detections']:
                bbox = det['bbox']
                conf = det['confidence']
                rect = patches.Rectangle(
                    (bbox[0], bbox[1]), bbox[2]-bbox[0], bbox[3]-bbox[1],
                    linewidth=2, edgecolor='red', facecolor='none'
                )
                ax.add_patch(rect)
                ax.text(bbox[0], bbox[1]-5, f'{conf:.2f}',
                       color='red', fontsize=8, weight='bold')

            # 设置标题
            ax.set_title(f'Window {window_info["window_id"]}: Frame {window_info["start_frame"]}-{window_info["end_frame"]}\n'
                        f'{len(window_info["detections"])} targets',
                        fontsize=10, fontweight='bold')
            ax.axis('off')

        # 隐藏多余的子图
        for i in range(total_windows, len(axes)):
            axes[i].axis('off')

        # 添加总体标题
        fig.suptitle(f'Sliding Window Detection Results\n'
                    f'Total Windows: {total_windows}, Total Detections: {len(merged_predictions["all_detections"])}',
                    fontsize=16, fontweight='bold')

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"滑窗检测结果已保存到: {save_path}")

        plt.show()

    def _visualize_sliding_window_with_tracking(self, merged_predictions: Dict, save_path: Optional[str] = None):
        """可视化带跟踪的滑窗检测结果"""
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'Microsoft YaHei']
        plt.rcParams['axes.unicode_minus'] = False

        windows_info = merged_predictions['windows_info']
        total_windows = merged_predictions['total_windows']

        # 创建子图布局
        if total_windows <= 4:
            rows, cols = 2, 2
        elif total_windows <= 6:
            rows, cols = 2, 3
        elif total_windows <= 9:
            rows, cols = 3, 3
        else:
            rows, cols = 4, 4

        fig, axes = plt.subplots(rows, cols, figsize=(5*cols, 4*rows))
        if total_windows == 1:
            axes = [axes]
        else:
            axes = axes.flatten()

        # 为不同轨迹分配颜色
        track_colors = {}
        colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray', 'olive', 'cyan']

        # 显示每个滑窗的结果
        for i, window_info in enumerate(windows_info):
            if i >= len(axes):
                break

            ax = axes[i]

            # 显示空间流图像
            ax.imshow(window_info['spatial_frame'], cmap='gray')

            # 绘制检测框（浅色）
            for det in window_info['detections']:
                bbox = det['bbox']
                conf = det['confidence']
                rect = patches.Rectangle(
                    (bbox[0], bbox[1]), bbox[2]-bbox[0], bbox[3]-bbox[1],
                    linewidth=1, edgecolor='lightblue', facecolor='none', alpha=0.5
                )
                ax.add_patch(rect)

            # 绘制跟踪框（深色，带轨迹ID）
            if 'tracks' in window_info:
                for track in window_info['tracks']:
                    track_id = track['track_id']
                    bbox = track['bbox']

                    # 为每个轨迹分配颜色
                    if track_id not in track_colors:
                        track_colors[track_id] = colors[len(track_colors) % len(colors)]

                    color = track_colors[track_id]

                    # 绘制跟踪框
                    rect = patches.Rectangle(
                        (bbox[0], bbox[1]), bbox[2]-bbox[0], bbox[3]-bbox[1],
                        linewidth=2, edgecolor=color, facecolor='none'
                    )
                    ax.add_patch(rect)

                    # 显示轨迹ID和置信度
                    ax.text(bbox[0], bbox[1]-5, f'T{track_id}: {track["avg_confidence"]:.2f}',
                           color=color, fontsize=8, weight='bold')

            # 设置标题
            det_count = len(window_info['detections'])
            track_count = len(window_info.get('tracks', []))
            ax.set_title(f'Window {window_info["window_id"]}: Frame {window_info["start_frame"]}-{window_info["end_frame"]}\n'
                        f'{det_count} detections, {track_count} tracks',
                        fontsize=10, fontweight='bold')
            ax.axis('off')

        # 隐藏多余的子图
        for i in range(total_windows, len(axes)):
            axes[i].axis('off')

        # 添加总体标题
        total_detections = len(merged_predictions["all_detections"])
        total_tracks = len(set([t['track_id'] for t in merged_predictions.get("all_tracks", [])]))
        fig.suptitle(f'Sliding Window Detection with Kalman Filter Tracking\n'
                    f'Total Windows: {total_windows}, Detections: {total_detections}, Unique Tracks: {total_tracks}',
                    fontsize=16, fontweight='bold')

        plt.tight_layout()

        if save_path:
            # 修改保存路径以区分跟踪结果
            if save_path.endswith('.png'):
                save_path = save_path.replace('.png', '_with_tracking.png')
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"带跟踪的滑窗检测结果已保存到: {save_path}")

        plt.show()

    def predict_large_image_sequence(self, image_paths: List[str], 
                                    crop_size: Tuple[int, int] = (280, 300),
                                    overlap_ratio: float = 0.2,
                                    save_path: Optional[str] = None,
                                    visualize: bool = True) -> Dict:
        """
        对大尺寸图像序列进行滑窗裁切预测
        
        Args:
            image_paths: 图像文件路径列表
            crop_size: 裁切窗口大小 (height, width)
            overlap_ratio: 重叠比例
            save_path: 结果保存路径
            visualize: 是否可视化结果
        """
        if len(image_paths) < self.sequence_length:
            raise ValueError(f"图像序列长度不足: {len(image_paths)} < {self.sequence_length}")
        
        # 加载第一张图像获取尺寸信息
        first_img = cv2.imread(image_paths[0], cv2.IMREAD_GRAYSCALE)
        if first_img is None:
            raise IOError(f"无法读取图像: {image_paths[0]}")

        if len(first_img.shape) == 3:
            img_h, img_w, _ = first_img.shape
        else:
            img_h, img_w = first_img.shape
        crop_h, crop_w = crop_size
        
        # 计算滑窗参数
        step_h = int(crop_h * (1 - overlap_ratio))
        step_w = int(crop_w * (1 - overlap_ratio))
        
        logger.info(f"大图预测: 图像尺寸 {img_w}×{img_h}, 裁切尺寸 {crop_w}×{crop_h}")
        logger.info(f"滑窗步长: {step_w}×{step_h}, 重叠率: {overlap_ratio}")
        
        all_detections = []
        crop_predictions = []
        
        # 滑窗遍历
        for y in range(0, img_h - crop_h + 1, step_h):
            for x in range(0, img_w - crop_w + 1, step_w):
                # 裁切当前窗口的所有帧
                crop_paths = []
                for i, img_path in enumerate(image_paths[:self.sequence_length]):
                    img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
                    crop = img[y:y+crop_h, x:x+crop_w]
                    
                    # 保存临时裁切图像
                    temp_dir = "temp_crops"
                    os.makedirs(temp_dir, exist_ok=True)
                    crop_path = os.path.join(temp_dir, f"crop_{y}_{x}_frame_{i}.png")
                    cv2.imwrite(crop_path, crop)
                    crop_paths.append(crop_path)
                
                # 对裁切区域进行预测
                try:
                    crop_result = self.predict_sequence(crop_paths, visualize=False)
                    
                    # 将检测结果坐标转换回原图坐标系
                    for det in crop_result['detections']:
                        det['bbox'][0] += x  # x1
                        det['bbox'][1] += y  # y1  
                        det['bbox'][2] += x  # x2
                        det['bbox'][3] += y  # y2
                        det['crop_region'] = (x, y, x+crop_w, y+crop_h)
                        all_detections.extend(crop_result['detections'])
                    
                    crop_predictions.append({
                        'region': (x, y, x+crop_w, y+crop_h),
                        'detections': crop_result['detections']
                    })
                    
                except Exception as e:
                    logger.warning(f"裁切区域 ({x},{y}) 预测失败: {e}")
                
                # 清理临时文件
                for temp_path in crop_paths:
                    if os.path.exists(temp_path):
                        os.remove(temp_path)
        
        # NMS去重
        all_detections = self._apply_nms_to_detections(all_detections)
        
        result = {
            'image_paths': image_paths,
            'image_size': (img_w, img_h),
            'crop_size': crop_size,
            'total_detections': len(all_detections),
            'detections': all_detections,
            'crop_predictions': crop_predictions
        }
        
        logger.info(f"大图预测完成: 检测到 {len(all_detections)} 个目标")

        if visualize or save_path:
            self._create_simple_visualization(result, save_path)

        return result

    def _apply_nms_to_detections(self, detections: List[Dict], iou_threshold: float = 0.5) -> List[Dict]:
        """对检测结果应用NMS去重"""
        if not detections:
            return []

        import torch

        # 提取边界框和置信度
        boxes = torch.tensor([det['bbox'] for det in detections])
        scores = torch.tensor([det['confidence'] for det in detections])

        # 应用NMS
        from torchvision.ops import nms
        keep_indices = nms(boxes, scores, iou_threshold)

        return [detections[i] for i in keep_indices.tolist()]

    def _visualize_large_image_results(self, result: Dict, save_path: Optional[str] = None):
        """可视化大图预测结果"""
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'Microsoft YaHei']
        plt.rcParams['axes.unicode_minus'] = False

        # 加载第一张原图用于可视化
        first_img_path = result['image_paths'][0]
        original_img = cv2.imread(first_img_path, cv2.IMREAD_GRAYSCALE)

        if original_img is None:
            logger.error(f"无法加载原图: {first_img_path}")
            return

        if len(original_img.shape) == 3:
            img_h, img_w, _ = original_img.shape
        else:
            img_h, img_w = original_img.shape
        crop_h, crop_w = result['crop_size']

        # 创建图像显示
        fig, axes = plt.subplots(1, 2, figsize=(20, 10))

        # 左图：显示原图和所有裁切区域
        axes[0].imshow(original_img, cmap='gray', vmin=0, vmax=255)
        axes[0].set_title(f'Original Image with Crop Regions\nSize: {img_w}×{img_h}',
                         fontsize=14, fontweight='bold')

        # 绘制所有裁切区域（浅色）
        for crop_pred in result['crop_predictions']:
            region = crop_pred['region']  # (x1, y1, x2, y2)
            has_detection = len(crop_pred['detections']) > 0
            color = 'red' if has_detection else 'lightblue'
            alpha = 0.6 if has_detection else 0.3
            linewidth = 2 if has_detection else 1

            rect = patches.Rectangle(
                (region[0], region[1]), region[2]-region[0], region[3]-region[1],
                linewidth=linewidth, edgecolor=color, facecolor='none', alpha=alpha
            )
            axes[0].add_patch(rect)

        # 右图：显示原图和检测结果
        axes[1].imshow(original_img, cmap='gray', vmin=0, vmax=255)
        axes[1].set_title(f'Detection Results\n{result["total_detections"]} targets detected',
                         fontsize=14, fontweight='bold')

        # 绘制检测结果
        colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'cyan']
        for i, det in enumerate(result['detections']):
            bbox = det['bbox']
            conf = det['confidence']
            color = colors[i % len(colors)]

            # 绘制检测框
            rect = patches.Rectangle(
                (bbox[0], bbox[1]), bbox[2]-bbox[0], bbox[3]-bbox[1],
                linewidth=3, edgecolor=color, facecolor='none'
            )
            axes[1].add_patch(rect)

            # 添加置信度标签
            axes[1].text(bbox[0], bbox[1]-10, f'MTD: {conf:.3f}',
                        color=color, fontsize=12, weight='bold',
                        bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))

        # 设置坐标轴范围和显示
        for ax in axes:
            ax.set_xlim(0, img_w)
            ax.set_ylim(img_h, 0)  # 翻转y轴，使图像正向显示
            ax.set_aspect('equal')  # 保持纵横比
            ax.grid(False)  # 关闭网格

        plt.tight_layout()

        # 保存结果
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
            logger.info(f"大图预测结果已保存到: {save_path}")

        plt.show()

        # 打印详细检测信息
        logger.info(f"大图预测详细结果:")
        logger.info(f"  原图尺寸: {img_w}×{img_h}")
        logger.info(f"  裁切尺寸: {crop_w}×{crop_h}")
        logger.info(f"  裁切区域数: {len(result['crop_predictions'])}")
        logger.info(f"  检测目标数: {result['total_detections']}")

        for i, det in enumerate(result['detections']):
            bbox = det['bbox']
            logger.info(f"  目标 {i+1}: 位置[{bbox[0]:.1f}, {bbox[1]:.1f}, {bbox[2]:.1f}, {bbox[3]:.1f}], "
                       f"置信度: {det['confidence']:.3f}")

    def _create_simple_visualization(self, result: Dict, save_path: Optional[str] = None):
        """创建简单的可视化结果"""
        import matplotlib.pyplot as plt
        import matplotlib.patches as patches

        # 加载第一张原图
        first_img_path = result['image_paths'][0]
        original_img = cv2.imread(first_img_path, cv2.IMREAD_GRAYSCALE)

        if original_img is None:
            logger.error(f"无法加载原图: {first_img_path}")
            return

        # 确保图像数据类型正确
        if original_img.dtype != np.uint8:
            original_img = ((original_img - original_img.min()) /
                           (original_img.max() - original_img.min()) * 255).astype(np.uint8)

        if len(original_img.shape) == 3:
            img_h, img_w, _ = original_img.shape
        else:
            img_h, img_w = original_img.shape

        # 创建单个图像显示
        fig, ax = plt.subplots(1, 1, figsize=(15, 8))

        # 显示原图
        ax.imshow(original_img, cmap='gray', vmin=0, vmax=255)
        ax.set_title(f'Large Image Detection Results\n'
                    f'Image Size: {img_w}×{img_h}, Detections: {result["total_detections"]}',
                    fontsize=14, fontweight='bold')

        # 绘制检测结果
        colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'cyan']
        for i, det in enumerate(result['detections']):
            bbox = det['bbox']
            conf = det['confidence']
            color = colors[i % len(colors)]

            # 绘制检测框
            rect = patches.Rectangle(
                (bbox[0], bbox[1]), bbox[2]-bbox[0], bbox[3]-bbox[1],
                linewidth=3, edgecolor=color, facecolor='none', alpha=0.8
            )
            ax.add_patch(rect)

            # 添加置信度标签
            ax.text(bbox[0], bbox[1]-5, f'Target {i+1}: {conf:.3f}',
                   color=color, fontsize=10, weight='bold',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))

        # 设置坐标轴
        ax.set_xlim(0, img_w)
        ax.set_ylim(img_h, 0)  # 翻转y轴
        ax.set_aspect('equal')
        ax.axis('on')

        plt.tight_layout()

        # 保存结果
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
            logger.info(f"可视化结果已保存到: {save_path}")

        # 不显示窗口，直接关闭
        plt.close()

        # 打印检测信息
        logger.info(f"检测结果:")
        for i, det in enumerate(result['detections']):
            bbox = det['bbox']
            logger.info(f"  目标 {i+1}: 位置[{bbox[0]:.1f}, {bbox[1]:.1f}, {bbox[2]:.1f}, {bbox[3]:.1f}], "
                       f"置信度: {det['confidence']:.3f}")

    def predict_large_image_with_multiscale(self, image_paths: List[str],
                                          crop_sizes: List[Tuple[int, int]] = None,
                                          overlap_ratio: float = 0.2,
                                          save_path: Optional[str] = None,
                                          visualize: bool = True) -> Dict:
        """
        多尺度大图预测

        Args:
            image_paths: 图像文件路径列表
            crop_sizes: 多个裁切尺寸列表，默认使用[(280, 300), (320, 360), (240, 270)]
            overlap_ratio: 重叠比例
            save_path: 结果保存路径
            visualize: 是否可视化结果
        """
        if crop_sizes is None:
            crop_sizes = [(280, 300), (320, 360), (240, 270)]  # 多尺度

        logger.info(f"开始多尺度大图预测，尺度数量: {len(crop_sizes)}")

        all_scale_detections = []
        scale_results = []

        for i, crop_size in enumerate(crop_sizes):
            logger.info(f"处理尺度 {i+1}/{len(crop_sizes)}: {crop_size[1]}×{crop_size[0]}")

            # 对当前尺度进行预测
            scale_result = self.predict_large_image_sequence(
                image_paths=image_paths,
                crop_size=crop_size,
                overlap_ratio=overlap_ratio,
                visualize=False
            )

            # 为每个检测结果添加尺度信息
            for det in scale_result['detections']:
                det['scale'] = crop_size
                det['scale_id'] = i

            all_scale_detections.extend(scale_result['detections'])
            scale_results.append(scale_result)

            logger.info(f"  尺度 {i+1} 检测到 {len(scale_result['detections'])} 个目标")

        # 跨尺度NMS去重
        logger.info("执行跨尺度NMS去重...")
        final_detections = self._apply_nms_to_detections(all_scale_detections, iou_threshold=0.3)

        # 合并结果
        multiscale_result = {
            'image_paths': image_paths,
            'crop_sizes': crop_sizes,
            'scale_results': scale_results,
            'total_detections': len(final_detections),
            'detections': final_detections,
            'image_size': scale_results[0]['image_size'] if scale_results else None
        }

        logger.info(f"多尺度预测完成: 总共检测到 {len(final_detections)} 个目标")

        if visualize or save_path:
            self._visualize_multiscale_results(multiscale_result, save_path)

        return multiscale_result

    def _visualize_multiscale_results(self, result: Dict, save_path: Optional[str] = None):
        """可视化多尺度预测结果"""
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'Microsoft YaHei']
        plt.rcParams['axes.unicode_minus'] = False

        # 加载第一张原图用于可视化
        first_img_path = result['image_paths'][0]
        original_img = cv2.imread(first_img_path, cv2.IMREAD_GRAYSCALE)

        if original_img is None:
            logger.error(f"无法加载原图: {first_img_path}")
            return

        if len(original_img.shape) == 3:
            img_h, img_w, _ = original_img.shape
        else:
            img_h, img_w = original_img.shape
        num_scales = len(result['crop_sizes'])

        # 创建子图布局
        fig, axes = plt.subplots(2, num_scales + 1, figsize=(6*(num_scales+1), 12))
        if num_scales == 0:
            return

        # 第一行：显示各个尺度的检测结果
        for i, scale_result in enumerate(result['scale_results']):
            ax = axes[0, i]
            ax.imshow(original_img, cmap='gray')

            crop_size = result['crop_sizes'][i]
            ax.set_title(f'Scale {i+1}: {crop_size[1]}×{crop_size[0]}\n'
                        f'{len(scale_result["detections"])} detections',
                        fontsize=12, fontweight='bold')

            # 绘制该尺度的检测结果
            colors = ['red', 'blue', 'green', 'orange', 'purple']
            for j, det in enumerate(scale_result['detections']):
                bbox = det['bbox']
                color = colors[j % len(colors)]
                rect = patches.Rectangle(
                    (bbox[0], bbox[1]), bbox[2]-bbox[0], bbox[3]-bbox[1],
                    linewidth=2, edgecolor=color, facecolor='none'
                )
                ax.add_patch(rect)
                ax.text(bbox[0], bbox[1]-5, f'{det["confidence"]:.2f}',
                       color=color, fontsize=8, weight='bold')

            ax.set_xlim(0, img_w)
            ax.set_ylim(img_h, 0)
            ax.axis('on')

        # 第一行最后一个：显示最终融合结果
        ax = axes[0, num_scales]
        ax.imshow(original_img, cmap='gray')
        ax.set_title(f'Final Result (After NMS)\n{result["total_detections"]} targets',
                    fontsize=12, fontweight='bold')

        # 绘制最终检测结果
        colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'cyan']
        for i, det in enumerate(result['detections']):
            bbox = det['bbox']
            color = colors[i % len(colors)]
            rect = patches.Rectangle(
                (bbox[0], bbox[1]), bbox[2]-bbox[0], bbox[3]-bbox[1],
                linewidth=3, edgecolor=color, facecolor='none'
            )
            ax.add_patch(rect)
            ax.text(bbox[0], bbox[1]-10, f'MTD: {det["confidence"]:.3f}',
                   color=color, fontsize=10, weight='bold',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))

        ax.set_xlim(0, img_w)
        ax.set_ylim(img_h, 0)
        ax.axis('on')

        # 第二行：显示各尺度的裁切区域分布
        for i, scale_result in enumerate(result['scale_results']):
            ax = axes[1, i]
            ax.imshow(original_img, cmap='gray', alpha=0.7)

            crop_size = result['crop_sizes'][i]
            ax.set_title(f'Crop Regions: {crop_size[1]}×{crop_size[0]}\n'
                        f'{len(scale_result["crop_predictions"])} regions',
                        fontsize=12, fontweight='bold')

            # 绘制裁切区域
            for crop_pred in scale_result['crop_predictions']:
                region = crop_pred['region']
                has_detection = len(crop_pred['detections']) > 0
                color = 'red' if has_detection else 'lightblue'
                alpha = 0.6 if has_detection else 0.3

                rect = patches.Rectangle(
                    (region[0], region[1]), region[2]-region[0], region[3]-region[1],
                    linewidth=1, edgecolor=color, facecolor='none', alpha=alpha
                )
                ax.add_patch(rect)

            ax.set_xlim(0, img_w)
            ax.set_ylim(img_h, 0)
            ax.axis('on')

        # 第二行最后一个：显示统计信息
        ax = axes[1, num_scales]
        ax.axis('off')

        # 创建统计文本
        stats_text = f"Multi-Scale Detection Statistics\n\n"
        stats_text += f"Original Image: {img_w}×{img_h}\n"
        stats_text += f"Number of Scales: {num_scales}\n\n"

        for i, (scale_result, crop_size) in enumerate(zip(result['scale_results'], result['crop_sizes'])):
            stats_text += f"Scale {i+1}: {crop_size[1]}×{crop_size[0]}\n"
            stats_text += f"  Crop regions: {len(scale_result['crop_predictions'])}\n"
            stats_text += f"  Detections: {len(scale_result['detections'])}\n\n"

        stats_text += f"Final Results (After NMS):\n"
        stats_text += f"  Total detections: {result['total_detections']}\n\n"

        # 按尺度统计最终结果
        scale_counts = {}
        for det in result['detections']:
            scale_id = det.get('scale_id', 0)
            scale_counts[scale_id] = scale_counts.get(scale_id, 0) + 1

        stats_text += "Detections by scale:\n"
        for scale_id, count in scale_counts.items():
            crop_size = result['crop_sizes'][scale_id]
            stats_text += f"  Scale {scale_id+1} ({crop_size[1]}×{crop_size[0]}): {count}\n"

        ax.text(0.1, 0.9, stats_text, transform=ax.transAxes, fontsize=10,
               verticalalignment='top', fontfamily='monospace',
               bbox=dict(boxstyle="round,pad=0.5", facecolor='lightgray', alpha=0.8))

        plt.tight_layout()

        # 保存结果
        if save_path:
            if save_path.endswith('.png'):
                save_path = save_path.replace('.png', '_multiscale.png')
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"多尺度预测结果已保存到: {save_path}")

        plt.show()


def predict_from_directory(predictor: SARMTDPredictor,
                          scene_dir: str, 
                          output_dir: str = "prediction_results"):
    """
    从目录中预测所有场景
    
    Args:
        predictor: 预测器实例
        scene_dir: 场景目录路径
        output_dir: 输出目录
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取所有图像文件
    image_files = []
    for ext in ['*.tif', '*.png', '*.jpg', '*.jpeg']:
        image_files.extend(Path(scene_dir).glob(ext))
    
    if not image_files:
        logger.error(f"在目录 {scene_dir} 中未找到图像文件")
        return
    
    # 按文件名排序
    image_files.sort()
    image_paths = [str(f) for f in image_files]
    
    logger.info(f"找到 {len(image_paths)} 个图像文件")
    
    # 执行预测
    save_path = os.path.join(output_dir, f"prediction_{Path(scene_dir).name}.png")
    predictions = predictor.predict_sequence(image_paths, save_path=save_path)
    
    # 保存预测结果到文件
    result_file = os.path.join(output_dir, f"results_{Path(scene_dir).name}.txt")
    with open(result_file, 'w', encoding='utf-8') as f:
        f.write(f"场景: {scene_dir}\n")
        f.write(f"输入图像数量: {len(image_paths)}\n")

        # 检查是否为滑窗结果
        if 'total_windows' in predictions:
            # 滑窗检测结果
            f.write(f"检测模式: 滑窗检测\n")
            f.write(f"滑窗数量: {predictions['total_windows']}\n")
            f.write(f"序列长度: {predictions['sequence_length']}\n")
            f.write(f"滑窗步长: {predictions['sliding_step']}\n")
            f.write(f"总检测目标数量: {len(predictions['all_detections'])}\n\n")

            # 按窗口分组显示结果
            f.write("各滑窗检测结果:\n")
            f.write("=" * 50 + "\n")
            for window_info in predictions['windows_info']:
                f.write(f"\n滑窗 {window_info['window_id']} (帧 {window_info['start_frame']}-{window_info['end_frame']}):\n")
                f.write(f"  检测到的目标数量: {len(window_info['detections'])}\n")

                for i, det in enumerate(window_info['detections']):
                    bbox = det['bbox']
                    f.write(f"  目标 {i+1}:\n")
                    f.write(f"    边界框: [{bbox[0]:.1f}, {bbox[1]:.1f}, {bbox[2]:.1f}, {bbox[3]:.1f}]\n")
                    f.write(f"    置信度: {det['confidence']:.3f}\n")
                    f.write(f"    类别: {det['class']}\n")

                if len(window_info['detections']) == 0:
                    f.write("  未检测到目标\n")
                f.write("\n")

            # 汇总所有检测结果
            f.write("所有检测结果汇总:\n")
            f.write("=" * 50 + "\n")
            for i, det in enumerate(predictions['all_detections']):
                bbox = det['bbox']
                f.write(f"目标 {i+1} (滑窗 {det['window_id']}, 起始帧 {det['start_frame']}):\n")
                f.write(f"  边界框: [{bbox[0]:.1f}, {bbox[1]:.1f}, {bbox[2]:.1f}, {bbox[3]:.1f}]\n")
                f.write(f"  置信度: {det['confidence']:.3f}\n")
                f.write(f"  类别: {det['class']}\n\n")
        else:
            # 单序列检测结果
            f.write(f"检测模式: 单序列检测\n")
            f.write(f"检测到的目标数量: {len(predictions['detections'])}\n\n")

            for i, det in enumerate(predictions['detections']):
                bbox = det['bbox']
                f.write(f"目标 {i+1}:\n")
                f.write(f"  边界框: [{bbox[0]:.1f}, {bbox[1]:.1f}, {bbox[2]:.1f}, {bbox[3]:.1f}]\n")
                f.write(f"  置信度: {det['confidence']:.3f}\n")
                f.write(f"  类别: {det['class']}\n\n")
    
    logger.info(f"预测结果已保存到: {result_file}")
    return predictions


def predict_large_image_from_directory(predictor: SARMTDPredictor,
                                     scene_dir: str,
                                     output_dir: str = "large_image_results",
                                     crop_size: Tuple[int, int] = (280, 300),
                                     overlap_ratio: float = 0.2,
                                     multiscale: bool = False,
                                     nms_threshold: float = 0.5):
    """
    从目录中对大图进行预测

    Args:
        predictor: 预测器实例
        scene_dir: 场景目录路径
        output_dir: 输出目录
        crop_size: 裁切窗口大小
        overlap_ratio: 重叠比例
        multiscale: 是否使用多尺度预测
        nms_threshold: NMS阈值
    """
    os.makedirs(output_dir, exist_ok=True)

    # 获取所有图像文件
    image_files = []
    for ext in ['*.tif', '*.png', '*.jpg', '*.jpeg']:
        image_files.extend(Path(scene_dir).glob(ext))

    if not image_files:
        logger.error(f"在目录 {scene_dir} 中未找到图像文件")
        return

    # 按文件名排序
    image_files.sort()
    image_paths = [str(f) for f in image_files]

    logger.info(f"找到 {len(image_paths)} 个图像文件")

    # 检查第一张图像的尺寸
    first_img = cv2.imread(image_paths[0], cv2.IMREAD_GRAYSCALE)
    if first_img is None:
        logger.error(f"无法读取第一张图像: {image_paths[0]}")
        return

    if len(first_img.shape) == 3:
        img_h, img_w, _ = first_img.shape
    else:
        img_h, img_w = first_img.shape
    logger.info(f"图像尺寸: {img_w}×{img_h}")
    logger.info(f"裁切尺寸: {crop_size[1]}×{crop_size[0]}")
    logger.info(f"重叠比例: {overlap_ratio}")
    logger.info(f"多尺度预测: {'启用' if multiscale else '禁用'}")

    # 执行预测
    scene_name = Path(scene_dir).name
    save_path = os.path.join(output_dir, f"large_image_prediction_{scene_name}.png")

    try:
        if multiscale:
            # 多尺度预测
            predictions = predictor.predict_large_image_with_multiscale(
                image_paths=image_paths,
                crop_sizes=[(280, 300), (320, 360), (240, 270)],
                overlap_ratio=overlap_ratio,
                save_path=save_path
            )
        else:
            # 单尺度预测
            predictions = predictor.predict_large_image_sequence(
                image_paths=image_paths,
                crop_size=crop_size,
                overlap_ratio=overlap_ratio,
                save_path=save_path
            )

        # 保存预测结果到文件
        result_file = os.path.join(output_dir, f"large_image_results_{scene_name}.txt")
        with open(result_file, 'w', encoding='utf-8') as f:
            f.write(f"大图预测结果\n")
            f.write("=" * 50 + "\n")
            f.write(f"场景: {scene_dir}\n")
            f.write(f"输入图像数量: {len(image_paths)}\n")
            f.write(f"图像尺寸: {img_w}×{img_h}\n")

            if multiscale:
                f.write(f"预测模式: 多尺度预测\n")
                f.write(f"使用尺度: {predictions['crop_sizes']}\n")
                f.write(f"总检测目标数量: {predictions['total_detections']}\n\n")

                # 按尺度统计
                scale_counts = {}
                for det in predictions['detections']:
                    scale_id = det.get('scale_id', 0)
                    scale_counts[scale_id] = scale_counts.get(scale_id, 0) + 1

                f.write("各尺度检测结果:\n")
                for scale_id, count in scale_counts.items():
                    crop_size_info = predictions['crop_sizes'][scale_id]
                    f.write(f"  尺度 {scale_id+1} ({crop_size_info[1]}×{crop_size_info[0]}): {count} 个目标\n")
                f.write("\n")
            else:
                f.write(f"预测模式: 单尺度预测\n")
                f.write(f"裁切尺寸: {crop_size[1]}×{crop_size[0]}\n")
                f.write(f"重叠比例: {overlap_ratio}\n")
                f.write(f"裁切区域数量: {len(predictions['crop_predictions'])}\n")
                f.write(f"检测目标数量: {predictions['total_detections']}\n\n")

            # 详细检测结果
            f.write("检测目标详情:\n")
            f.write("-" * 30 + "\n")
            for i, det in enumerate(predictions['detections']):
                bbox = det['bbox']
                f.write(f"目标 {i+1}:\n")
                f.write(f"  边界框: [{bbox[0]:.1f}, {bbox[1]:.1f}, {bbox[2]:.1f}, {bbox[3]:.1f}]\n")
                f.write(f"  置信度: {det['confidence']:.3f}\n")
                f.write(f"  类别: {det['class']}\n")
                if 'scale' in det:
                    f.write(f"  检测尺度: {det['scale'][1]}×{det['scale'][0]}\n")
                f.write("\n")

        logger.info(f"大图预测结果已保存到: {result_file}")
        return predictions

    except Exception as e:
        logger.error(f"大图预测失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='SAR图像动目标检测预测')
    parser.add_argument('--model', type=str, default='runs/detect/train14/weights/best.pt',
                       help='模型权重文件路径')
    parser.add_argument('--input', type=str, default='wholePicture/sixth/output0001-16384',
                       help='输入图像序列目录路径')
    parser.add_argument('--output', type=str, default='prediction_results',
                       help='输出目录')
    parser.add_argument('--sequence_length', type=int, default=4,
                       help='输入序列长度')
    parser.add_argument('--conf', type=float, default=0.25,
                       help='置信度阈值')
    parser.add_argument('--iou', type=float, default=0.45,
                       help='IoU阈值')
    parser.add_argument('--no-sliding-window', action='store_true',
                       help='禁用滑窗检测，仅使用前N帧')
    parser.add_argument('--sliding-step', type=int, default=2,
                       help='滑窗步长')
    parser.add_argument('--keep-only-best', action='store_true',
                       help='只保留置信度最高的目标')
    parser.add_argument('--use-kalman-filter', action='store_true', default=True,
                       help='使用卡尔曼滤波进行目标跟踪')
    parser.add_argument('--no-kalman-filter', action='store_true',
                       help='禁用卡尔曼滤波跟踪')
    parser.add_argument('--max-disappeared', type=int, default=30,
                       help='目标消失的最大帧数')
    parser.add_argument('--max-distance', type=float, default=50.0,
                       help='数据关联的最大距离')

    # 大图预测相关参数
    parser.add_argument('--large-image', action='store_true',
                       help='启用大图预测模式（对原始大尺寸图像进行滑窗裁切预测）')
    parser.add_argument('--crop-size', type=str, default='280,300',
                       help='裁切窗口大小，格式: height,width (默认: 280,300)')
    parser.add_argument('--overlap-ratio', type=float, default=0.2,
                       help='裁切窗口重叠比例 (默认: 0.2)')
    parser.add_argument('--multiscale', action='store_true',
                       help='启用多尺度预测')
    parser.add_argument('--nms-threshold', type=float, default=0.5,
                       help='NMS IoU阈值 (默认: 0.5)')

    args = parser.parse_args()

    try:
        # 确定是否使用卡尔曼滤波
        use_kalman = args.use_kalman_filter and not args.no_kalman_filter

        # 解析裁切尺寸
        crop_size = tuple(map(int, args.crop_size.split(',')))
        if len(crop_size) != 2:
            raise ValueError("裁切尺寸格式错误，应为 height,width")

        # 创建预测器
        predictor = SARMTDPredictor(
            model_path=args.model,
            sequence_length=args.sequence_length,
            conf_threshold=args.conf,
            iou_threshold=args.iou,
            use_sliding_window=not args.no_sliding_window,
            sliding_step=args.sliding_step,
            keep_only_best=args.keep_only_best,
            use_kalman_filter=use_kalman,
            max_disappeared=args.max_disappeared,
            max_distance=args.max_distance
        )

        # 根据模式执行预测
        if args.large_image:
            predict_large_image_from_directory(
                predictor=predictor,
                scene_dir=args.input,
                output_dir=args.output,
                crop_size=crop_size,
                overlap_ratio=args.overlap_ratio,
                multiscale=args.multiscale,
                nms_threshold=args.nms_threshold
            )
        else:
            predict_from_directory(predictor, args.input, args.output)
        
    except Exception as e:
        logger.error(f"预测失败: {e}")
        import traceback
        logger.error(traceback.format_exc())


if __name__ == '__main__':
    main()
