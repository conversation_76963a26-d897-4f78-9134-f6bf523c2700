#!/usr/bin/env python3
# predict_large_image.py
"""
大尺寸SAR图像动目标检测预测脚本
专门用于处理原始大尺寸图像（如1024×3277）的动目标检测
"""

import os
import sys
import argparse
import logging
from pathlib import Path
from typing import List, Tuple

# 导入预测器
from predict_sar_mtd import SARMTDPredictor, predict_large_image_from_directory

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='大尺寸SAR图像动目标检测预测',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:

1. 基本大图预测（单尺度）:
   python predict_large_image.py --input wholePicture/sixth/output0001-16384 --output large_results

2. 多尺度大图预测:
   python predict_large_image.py --input wholePicture/sixth/output0001-16384 --multiscale --output large_results

3. 自定义裁切尺寸:
   python predict_large_image.py --input wholePicture/sixth/output0001-16384 --crop-size 320,360 --overlap 0.3

4. 调整检测阈值:
   python predict_large_image.py --input wholePicture/sixth/output0001-16384 --conf 0.3 --nms-threshold 0.4

注意事项:
- 输入目录应包含时间序列的SAR图像文件
- 模型是在280×300尺寸的图像上训练的
- 大图会被自动裁切成训练尺寸进行预测
- 使用NMS去除重叠检测结果
        """
    )
    
    # 基本参数
    parser.add_argument('--input', type=str, required=True,
                       help='输入图像序列目录路径')
    parser.add_argument('--output', type=str, default='large_image_results',
                       help='输出目录 (默认: large_image_results)')
    parser.add_argument('--model', type=str, default='runs/detect/train14/weights/best.pt',
                       help='模型权重文件路径')
    
    # 预测参数
    parser.add_argument('--sequence-length', type=int, default=4,
                       help='输入序列长度 (默认: 4)')
    parser.add_argument('--conf', type=float, default=0.25,
                       help='置信度阈值 (默认: 0.25)')
    parser.add_argument('--iou', type=float, default=0.45,
                       help='IoU阈值 (默认: 0.45)')
    
    # 大图预测参数
    parser.add_argument('--crop-size', type=str, default='280,300',
                       help='裁切窗口大小，格式: height,width (默认: 280,300)')
    parser.add_argument('--overlap', type=float, default=0.2,
                       help='裁切窗口重叠比例 (默认: 0.2)')
    parser.add_argument('--multiscale', action='store_true',
                       help='启用多尺度预测（使用多个裁切尺寸）')
    parser.add_argument('--nms-threshold', type=float, default=0.5,
                       help='NMS IoU阈值，用于去除重叠检测 (默认: 0.5)')
    
    # 高级参数
    parser.add_argument('--no-sliding-window', action='store_true',
                       help='禁用时间滑窗检测，仅使用前N帧')
    parser.add_argument('--sliding-step', type=int, default=2,
                       help='时间滑窗步长 (默认: 2)')
    parser.add_argument('--keep-only-best', action='store_true',
                       help='只保留置信度最高的目标')
    
    # 跟踪参数
    parser.add_argument('--use-kalman-filter', action='store_true', default=True,
                       help='使用卡尔曼滤波进行目标跟踪')
    parser.add_argument('--no-kalman-filter', action='store_true',
                       help='禁用卡尔曼滤波跟踪')
    parser.add_argument('--max-disappeared', type=int, default=30,
                       help='目标消失的最大帧数 (默认: 30)')
    parser.add_argument('--max-distance', type=float, default=50.0,
                       help='数据关联的最大距离 (默认: 50.0)')
    
    # 可视化参数
    parser.add_argument('--no-visualize', action='store_true',
                       help='禁用结果可视化')
    
    args = parser.parse_args()
    
    try:
        # 检查输入目录
        if not os.path.exists(args.input):
            logger.error(f"输入目录不存在: {args.input}")
            return
        
        # 检查模型文件
        if not os.path.exists(args.model):
            logger.error(f"模型文件不存在: {args.model}")
            return
        
        # 解析裁切尺寸
        try:
            crop_size = tuple(map(int, args.crop_size.split(',')))
            if len(crop_size) != 2:
                raise ValueError()
        except ValueError:
            logger.error("裁切尺寸格式错误，应为 height,width，例如: 280,300")
            return
        
        # 确定是否使用卡尔曼滤波
        use_kalman = args.use_kalman_filter and not args.no_kalman_filter
        
        logger.info("=" * 60)
        logger.info("大尺寸SAR图像动目标检测预测")
        logger.info("=" * 60)
        logger.info(f"输入目录: {args.input}")
        logger.info(f"输出目录: {args.output}")
        logger.info(f"模型文件: {args.model}")
        logger.info(f"裁切尺寸: {crop_size[1]}×{crop_size[0]}")
        logger.info(f"重叠比例: {args.overlap}")
        logger.info(f"多尺度预测: {'启用' if args.multiscale else '禁用'}")
        logger.info(f"置信度阈值: {args.conf}")
        logger.info(f"NMS阈值: {args.nms_threshold}")
        logger.info(f"卡尔曼滤波: {'启用' if use_kalman else '禁用'}")
        logger.info("=" * 60)
        
        # 创建预测器
        predictor = SARMTDPredictor(
            model_path=args.model,
            sequence_length=args.sequence_length,
            conf_threshold=args.conf,
            iou_threshold=args.iou,
            use_sliding_window=not args.no_sliding_window,
            sliding_step=args.sliding_step,
            keep_only_best=args.keep_only_best,
            use_kalman_filter=use_kalman,
            max_disappeared=args.max_disappeared,
            max_distance=args.max_distance
        )
        
        # 执行大图预测
        predictions = predict_large_image_from_directory(
            predictor=predictor,
            scene_dir=args.input,
            output_dir=args.output,
            crop_size=crop_size,
            overlap_ratio=args.overlap,
            multiscale=args.multiscale,
            nms_threshold=args.nms_threshold
        )
        
        if predictions is not None:
            logger.info("=" * 60)
            logger.info("预测完成！")
            logger.info(f"检测到 {predictions['total_detections']} 个动目标")
            logger.info(f"结果已保存到: {args.output}")
            logger.info("=" * 60)
        else:
            logger.error("预测失败！")
            
    except KeyboardInterrupt:
        logger.info("用户中断预测")
    except Exception as e:
        logger.error(f"预测过程中出现错误: {e}")
        import traceback
        logger.error(traceback.format_exc())


if __name__ == '__main__':
    main()
