# 大尺寸SAR图像动目标检测指南

## 问题背景

您的模型在280×300像素的裁切图像上训练，但需要对1024×3277像素的原始大图进行预测。直接对大图预测效果不好，因为：

1. **尺寸不匹配**：模型期望280×300输入，但原图是1024×3277
2. **特征尺度差异**：大图中的目标相对更小，特征不明显
3. **内存限制**：大图直接输入会占用过多内存

## 解决方案

我们实现了**滑窗裁切预测**方法：

1. **滑窗裁切**：将大图按280×300尺寸滑窗裁切
2. **重叠预测**：裁切窗口之间有重叠，避免边界目标丢失
3. **坐标映射**：将裁切区域的检测结果映射回原图坐标
4. **NMS去重**：去除重叠区域的重复检测
5. **多尺度支持**：使用多个裁切尺寸提高检测率

## 使用方法

### 1. 基本大图预测

```bash
python predict_large_image.py --input wholePicture/sixth/output0001-16384 --output large_results
```

### 2. 多尺度预测（推荐）

```bash
python predict_large_image.py --input wholePicture/sixth/output0001-16384 --multiscale --output large_results
```

### 3. 自定义参数

```bash
python predict_large_image.py \
    --input wholePicture/sixth/output0001-16384 \
    --output large_results \
    --crop-size 320,360 \
    --overlap 0.3 \
    --conf 0.3 \
    --nms-threshold 0.4
```

## 参数说明

### 核心参数

- `--input`: 输入图像序列目录
- `--output`: 输出结果目录
- `--model`: 模型权重文件路径

### 大图预测参数

- `--crop-size`: 裁切尺寸，格式为`height,width`（默认：280,300）
- `--overlap`: 重叠比例（默认：0.2，即20%重叠）
- `--multiscale`: 启用多尺度预测
- `--nms-threshold`: NMS阈值，用于去重（默认：0.5）

### 检测参数

- `--conf`: 置信度阈值（默认：0.25）
- `--iou`: IoU阈值（默认：0.45）
- `--sequence-length`: 时间序列长度（默认：4）

## 预测流程

### 单尺度预测流程

```
原图(1024×3277) → 滑窗裁切(280×300) → 模型预测 → 坐标映射 → NMS去重 → 最终结果
```

### 多尺度预测流程

```
原图(1024×3277) → 多尺度裁切 → 分别预测 → 结果合并 → 跨尺度NMS → 最终结果
                 ↓
            280×300, 320×360, 240×270
```

## 输出结果

### 文件输出

1. **可视化图像**：`large_image_prediction_[场景名].png`
2. **详细结果**：`large_image_results_[场景名].txt`

### 结果格式

```
大图预测结果
==================================================
场景: wholePicture/sixth/output0001-16384
输入图像数量: 16
图像尺寸: 3277×1024
预测模式: 多尺度预测
使用尺度: [(280, 300), (320, 360), (240, 270)]
总检测目标数量: 3

各尺度检测结果:
  尺度 1 (300×280): 1 个目标
  尺度 2 (360×320): 2 个目标
  尺度 3 (270×240): 1 个目标

检测目标详情:
------------------------------
目标 1:
  边界框: [1245.0, 456.0, 1289.0, 498.0]
  置信度: 0.856
  类别: 0
  检测尺度: 300×280
```

## 参数调优建议

### 1. 裁切尺寸选择

- **默认尺寸**：280×300（与训练数据一致）
- **稍大尺寸**：320×360（包含更多上下文）
- **稍小尺寸**：240×270（聚焦目标区域）

### 2. 重叠比例调整

- **低重叠**：0.1-0.2（速度快，可能漏检边界目标）
- **中等重叠**：0.2-0.3（平衡速度和准确性）
- **高重叠**：0.3-0.5（准确性高，速度慢）

### 3. 阈值设置

- **置信度阈值**：0.2-0.4（根据检测效果调整）
- **NMS阈值**：0.3-0.6（控制去重强度）

### 4. 多尺度策略

- **启用条件**：目标尺寸变化大，单尺度效果不佳
- **尺度选择**：围绕训练尺寸±20%变化
- **计算成本**：多尺度会增加3倍计算时间

## 性能优化

### 1. 内存优化

- 使用临时文件存储裁切图像
- 及时清理中间结果
- 批量处理时控制并发数

### 2. 速度优化

- 减少重叠比例
- 使用单尺度预测
- 提高置信度阈值

### 3. 准确性优化

- 使用多尺度预测
- 增加重叠比例
- 降低置信度阈值
- 调整NMS阈值

## 常见问题

### Q1: 为什么大图直接预测效果不好？

A: 模型在280×300尺寸上训练，直接输入大图会导致：
- 目标相对变小，特征不明显
- 模型内部会自动resize，破坏原有特征
- 训练和预测的数据分布不一致

### Q2: 如何选择最佳的裁切尺寸？

A: 建议：
- 首先使用默认的280×300（与训练数据一致）
- 如果效果不佳，尝试多尺度预测
- 根据目标大小调整尺寸范围

### Q3: 多尺度预测什么时候使用？

A: 适用场景：
- 目标尺寸变化较大
- 单尺度预测漏检较多
- 对准确性要求高于速度

### Q4: 如何处理重复检测？

A: 系统自动使用NMS去重：
- 调整`--nms-threshold`参数
- 较小值（0.3）：去重更严格
- 较大值（0.6）：保留更多检测

## 示例命令

```bash
# 基本预测
python predict_large_image.py --input your_image_dir --output results

# 高精度预测
python predict_large_image.py --input your_image_dir --multiscale --overlap 0.3 --conf 0.2

# 快速预测
python predict_large_image.py --input your_image_dir --overlap 0.1 --conf 0.4

# 自定义尺寸
python predict_large_image.py --input your_image_dir --crop-size 320,360 --overlap 0.25
```
